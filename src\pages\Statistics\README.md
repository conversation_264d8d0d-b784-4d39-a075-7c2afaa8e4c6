# 📊 统计分析页面

## 🎯 功能概述

统计分析页面提供了全面的问卷数据分析和可视化展示，帮助管理员深入了解学校和教师的评价情况。

## 🚀 统计任务机制 (新增)

为了解决实时统计查询的性能问题，系统引入了统计任务机制：

### 核心特性

- **手动触发**：用户可以手动触发统计计算
- **异步执行**：统计任务在后台异步执行，不阻塞用户操作
- **缓存机制**：计算结果缓存到数据库，后续查询直接从缓存读取
- **状态监控**：实时显示统计任务的执行状态
- **性能优化**：大大提升大数据量下的查询性能

### 工作流程

1. 用户在筛选表单中点击"触发统计"按钮
2. 系统启动异步统计任务，状态显示为"等待计算"或"计算中"
3. 后台完成统计计算后，状态更新为"计算完成"
4. 用户查询时优先使用缓存数据，提升响应速度

### 状态说明

- **pending**: 等待计算
- **calculating**: 计算中
- **completed**: 计算完成
- **failed**: 计算失败

## ✨ 主要功能

### 1. 数据筛选功能

- **月份选择器**：支持选择最近 12 个月的数据
- **科目筛选**：支持按教师科目筛选
- **部门筛选**：支持按教师部门筛选
- **实时刷新**：筛选条件变更时自动刷新所有统计数据
- **服务端分页**：支持大数据量的分页展示

### 2. 学校整体统计卡片

- **学校平均分**：显示学校整体评价平均分
- **参与人数**：显示已完成评价的人数和学生总数
- **完成率**：基于学生总数计算的真实完成率
- **被评教师数**：显示参与评价的教师总数和教师平均分
- **未填写学生**：快速查看未填写学生统计信息

### 3. 月度评分趋势图

- **折线图展示**：使用 Ant Design Charts 展示月度趋势
- **双线对比**：同时显示学校平均分和教师平均分趋势
- **交互功能**：支持悬停查看详细数据
- **时间滑块**：支持选择时间范围查看

### 4. 学科平均分对比图

- **柱状图展示**：按学科分组显示平均分对比
- **自动排序**：按平均分降序排列
- **数据标签**：显示具体分值和教师数量
- **响应式设计**：自适应不同屏幕尺寸

### 5. 未填写学生统计

- **总体统计**：显示未填写学生总数和各年级完成情况
- **年级统计**：按年级显示完成率和未填写人数
- **班级详情**：展开查看各班级的详细未填写学生名单
- **学生信息**：显示学生姓名、代码、年级、班级等信息
- **实时刷新**：支持手动刷新最新数据

### 6. 教师评分排行榜

- **排名展示**：显示教师评分排名（前三名有特殊图标）
- **多维度信息**：教师姓名、学科、部门、平均分、评价人数
- **服务端分页**：支持大数据量的服务端分页
- **服务端排序**：支持按平均分和评价人数的服务端排序
- **筛选功能**：支持按科目和部门筛选

### 7. 教师详情模态框

- **基本统计**：平均分、评价总数、推荐率、教师信息
- **评分分布饼图**：显示各分数段的占比情况
- **关键词云**：展示家长评价中的高频关键词
- **交互设计**：点击教师行或详情按钮打开

## 🛠️ 技术实现

### 组件架构

```
src/pages/Statistics/
├── index.tsx                    # 主页面组件
├── index.less                   # 样式文件
├── components/
│   ├── FilterForm.tsx           # 数据筛选表单
│   ├── SchoolOverview.tsx       # 学校整体统计卡片
│   ├── IncompleteStudents.tsx   # 未填写学生统计组件
│   ├── TeacherRanking.tsx       # 教师排行榜
│   └── TeacherDetailModal.tsx   # 教师详情模态框
└── README.md                    # 说明文档
```

### 数据模型

- **状态管理**：使用 umi dva model 进行状态管理
- **数据缓存**：支持学校列表数据缓存
- **错误处理**：完善的错误提示和加载状态
- **类型安全**：完整的 TypeScript 类型定义

### 图表库

- **Ant Design Charts**：基于 G2Plot 的 React 图表库
- **饼图**：评分分布展示
- **词云图**：关键词可视化

## 📱 响应式设计

- **桌面端**：完整功能展示，多列布局
- **平板端**：自适应布局调整
- **移动端**：单列布局，优化触摸操作

## 🎨 UI/UX 特性

- **现代化设计**：遵循 Ant Design 设计规范
- **动画效果**：图表加载和交互动画
- **主题一致性**：与整体系统保持一致
- **无障碍支持**：键盘导航和屏幕阅读器支持

## 🔧 配置说明

### API 接口

- `GET /api/statistics/school` - 获取学校统计数据（包含未填写学生统计）
- `GET /api/statistics/incomplete-students` - 获取未填写学生详细统计
- `GET /api/statistics/teacher-ranking` - 获取教师排名（支持分页和排序）
- `GET /api/statistics/teacher` - 获取教师统计数据
- `GET /api/statistics/teacher/:teacherId/distribution` - 获取评分分布
- `GET /api/statistics/teacher/:teacherId/keywords` - 获取关键词数据
- `GET /api/statistics/trend` - 获取趋势分析数据

> 详细的接口实现指南请参考 `docs/backend-statistics-api.md`

### 路由配置

```typescript
{
  name: '统计分析',
  path: '/statistics',
  wrappers: ['@/wrappers/auth'],
  component: './Statistics',
}
```

## 🚀 使用说明

### 1. 访问页面

导航到 `/statistics` 查看统计分析页面

### 2. 筛选数据

- 自动使用当前用户所属学校数据
- 选择月份查看历史数据
- 点击"查询"按钮刷新数据

### 3. 查看统计

- 查看学校整体统计卡片
- 了解完成率和平均分情况

### 4. 教师排名

- 浏览教师评分排行榜
- 使用搜索功能查找特定教师
- 点击教师查看详细信息

### 5. 详细分析

- 查看教师评分分布
- 分析家长评价关键词
- 了解教师综合表现

## 🔄 数据更新

### 传统模式

- **实时性**：数据来源于最新的问卷响应
- **权限控制**：自动使用当前用户所属学校，确保数据安全
- **自动刷新**：筛选条件变更时自动更新所有相关数据

### 统计任务模式 (推荐)

- **缓存优先**：优先使用缓存的统计数据，提升查询性能
- **手动触发**：用户可以手动触发统计计算，确保数据最新
- **状态监控**：实时显示统计任务状态和最后更新时间
- **智能切换**：根据统计状态自动选择数据源

## 📋 新增 API 接口

### 统计任务相关接口

- `POST /api/statistics-task/trigger` - 触发统计计算
- `GET /api/statistics-task/status/{questionnaireId}` - 获取统计状态
- `GET /api/statistics-task/cached/{questionnaireId}` - 获取缓存统计数据
- `GET /api/statistics-task/incomplete-students` - 获取缓存的未填写学生列表

## 🎯 后续优化

- [ ] 添加数据导出功能
- [ ] 支持自定义时间范围选择
- [ ] 增加更多图表类型
- [ ] 添加数据对比功能
- [ ] 支持报告生成和打印
