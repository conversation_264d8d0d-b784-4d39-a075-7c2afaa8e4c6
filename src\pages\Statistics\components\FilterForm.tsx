import type { IStatisticsQuery, IStatisticsTaskStatusResponse } from '@/types/statistics';
import { ReloadOutlined, SearchOutlined, StopOutlined, ThunderboltOutlined } from '@ant-design/icons';
import { Button, Card, Col, Form, Row, Select, Space, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';

const { Option } = Select;

interface FilterFormProps {
  loading?: boolean;
  taskLoading?: boolean;
  isPolling?: boolean;
  statisticsTaskStatus?: IStatisticsTaskStatusResponse | null;
  onFilter: (values: IStatisticsQuery) => void;
  onReset: () => void;
  onTriggerStatistics?: (questionnaireId: number) => void;
  onRefreshStatus?: (questionnaireId: number) => void;
  onStopPolling?: () => void;
}

/**
 * 数据筛选表单组件
 */
const FilterForm: React.FC<FilterFormProps> = ({
  loading = false,
  taskLoading = false,
  isPolling = false,
  statisticsTaskStatus,
  onFilter,
  onReset,
  onTriggerStatistics,
  onRefreshStatus,
  onStopPolling,
}) => {
  const [form] = Form.useForm();

  // 处理筛选
  const handleFilter = (values: any) => {
    const filterParams: IStatisticsQuery = {
      month: values.month ? dayjs(values.month).format('YYYY-MM') : undefined,
      questionnaire_id: values.questionnaire_id,
    };
    onFilter(filterParams);
  };

  // 处理触发统计
  const handleTriggerStatistics = () => {
    const questionnaireId = form.getFieldValue('questionnaire_id');
    if (questionnaireId && onTriggerStatistics) {
      onTriggerStatistics(questionnaireId);
    }
  };

  // 处理刷新状态
  const handleRefreshStatus = () => {
    const questionnaireId = form.getFieldValue('questionnaire_id');
    if (questionnaireId && onRefreshStatus) {
      onRefreshStatus(questionnaireId);
    }
  };

  // 获取状态显示文本
  const getStatusText = () => {
    if (!statisticsTaskStatus) return '';

    let statusText = '';
    switch (statisticsTaskStatus.status) {
      case 'pending':
        statusText = '等待计算';
        break;
      case 'calculating':
        statusText = '计算中...';
        break;
      case 'completed':
        statusText = `计算完成 (${statisticsTaskStatus.last_calculated_at ? dayjs(statisticsTaskStatus.last_calculated_at).format('MM-DD HH:mm') : ''})`;
        break;
      case 'failed':
        statusText = '计算失败';
        break;
      default:
        statusText = '';
    }

    // 如果正在轮询，添加轮询标识
    if (isPolling && (statisticsTaskStatus.status === 'calculating' || statisticsTaskStatus.status === 'pending')) {
      statusText += ' (自动刷新中)';
    }

    return statusText;
  };

  // 获取状态颜色
  const getStatusColor = () => {
    if (!statisticsTaskStatus) return '';

    switch (statisticsTaskStatus.status) {
      case 'pending':
        return '#faad14';
      case 'calculating':
        return '#1890ff';
      case 'completed':
        return '#52c41a';
      case 'failed':
        return '#ff4d4f';
      default:
        return '';
    }
  };

  // 处理停止轮询
  const handleStopPolling = () => {
    if (onStopPolling) {
      onStopPolling();
    }
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  // 生成月份选项（最近12个月）
  const getMonthOptions = () => {
    const months = [];
    const current = dayjs();

    for (let i = 0; i < 12; i++) {
      const month = current.subtract(i, 'month');
      months.push({
        value: month.format('YYYY-MM'),
        label: month.format('YYYY年MM月'),
      });
    }

    return months;
  };

  // 初始化默认值
  useEffect(() => {
    form.setFieldsValue({
      month: dayjs().format('YYYY-MM'), // 默认当前月份
      questionnaire_id: 1, // 默认问卷ID，实际应该从问卷列表中获取
    });
  }, [form]);

  return (
    <Card style={{ marginBottom: 16 }}>
      <Form
        form={form}
        layout="inline"
        onFinish={handleFilter}
        style={{ width: '100%' }}
      >
        <Row gutter={16} style={{ width: '100%' }}>
          <Col span={6}>
            <Form.Item name="questionnaire_id" label="问卷">
              <Select
                placeholder="请选择问卷"
                style={{ width: '100%' }}
              >
                <Option value={1}>2024年度教师评价问卷</Option>
                {/* 实际应该从问卷列表API获取 */}
              </Select>
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item name="month" label="月份">
              <Select
                placeholder="请选择月份"
                allowClear
                style={{ width: '100%' }}
              >
                {getMonthOptions().map((month) => (
                  <Option key={month.value} value={month.value}>
                    {month.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                >
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  重置
                </Button>
                <Tooltip title="手动触发统计计算，系统将异步执行统计任务">
                  <Button
                    type="dashed"
                    icon={<ThunderboltOutlined />}
                    loading={taskLoading}
                    onClick={handleTriggerStatistics}
                  >
                    触发统计
                  </Button>
                </Tooltip>
                {isPolling && (
                  <Tooltip title="停止自动刷新统计状态">
                    <Button
                      type="default"
                      icon={<StopOutlined />}
                      onClick={handleStopPolling}
                      size="small"
                    >
                      停止轮询
                    </Button>
                  </Tooltip>
                )}
                {statisticsTaskStatus && (
                  <span style={{ color: getStatusColor(), marginLeft: 8 }}>
                    {getStatusText()}
                  </span>
                )}
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default FilterForm;
