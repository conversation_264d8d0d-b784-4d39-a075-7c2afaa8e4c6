import type { ISchoolStatistics, IStatisticsTaskStatusResponse } from '@/types/statistics';
import {
  ClockCircleOutlined,
  PercentageOutlined,
  TrophyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button, Card, Col, Row, Skeleton, Space, Statistic, Tag } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

interface SchoolOverviewProps {
  data: ISchoolStatistics | null;
  loading?: boolean;
  statisticsTaskStatus?: IStatisticsTaskStatusResponse | null;
  onShowIncompleteStudents?: () => void;
}

/**
 * 学校整体统计卡片组件
 */
const SchoolOverview: React.FC<SchoolOverviewProps> = ({
  data,
  loading = false,
  statisticsTaskStatus,
  onShowIncompleteStudents,
}) => {
  if (loading) {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        {[1, 2, 3, 4].map((item) => (
          <Col span={6} key={item}>
            <Card>
              <Skeleton active paragraph={{ rows: 2 }} />
            </Card>
          </Col>
        ))}
      </Row>
    );
  }

  if (!data) {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card>
            <div
              style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}
            >
              暂无统计数据
            </div>
          </Card>
        </Col>
      </Row>
    );
  }

  // 获取统计状态标签
  const getStatusTag = () => {
    if (!statisticsTaskStatus) return null;

    const { status, last_calculated_at } = statisticsTaskStatus;

    switch (status) {
      case 'pending':
        return <Tag color="orange" icon={<ClockCircleOutlined />}>等待计算</Tag>;
      case 'calculating':
        return <Tag color="blue" icon={<ClockCircleOutlined />}>计算中...</Tag>;
      case 'completed':
        return (
          <Tag color="green" icon={<ClockCircleOutlined />}>
            已完成 ({last_calculated_at ? dayjs(last_calculated_at).format('MM-DD HH:mm') : ''})
          </Tag>
        );
      case 'failed':
        return <Tag color="red" icon={<ClockCircleOutlined />}>计算失败</Tag>;
      default:
        return null;
    }
  };

  // 统一使用学校统计数据，确保完成率显示一致
  const completionData = {
    completion_rate: data?.completion_rate || 0,
    completed_responses: data?.completed_responses || 0,
    total_students: data?.total_students || 0,
  };

  console.log(data);
  return (
    <>
      {/* 统计状态显示 */}
      {statisticsTaskStatus && (
        <Card style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <span>统计状态：</span>
            {getStatusTag()}
            {statisticsTaskStatus.status === 'completed' && data && (
              <span style={{ color: '#999', fontSize: '12px' }}>
                完成率：{data.completion_rate?.toFixed(1) || 0}%
                ({data.completed_responses || 0}/{data.total_students || 0})
              </span>
            )}
          </div>
        </Card>
      )}

      <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <TrophyOutlined style={{ color: '#faad14', fontSize: 24 }} />
                学校平均分
              </div>
            }
            value={(data.school_average_score || '--') + '分'}
            precision={1}
            valueStyle={{ color: '#faad14' }}
          />
          <div
            style={{
              marginTop: 8,
              fontSize: '12px',
              color: '#999',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {data.sso_school_name || '学校'}
          </div>
        </Card>
      </Col>

      <Col span={6}>
        <Card>
          <Statistic
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <UserOutlined style={{ color: '#52c41a', fontSize: 24 }} />
                参与人数
              </div>
            }
            value={(completionData.completed_responses || '--') + '人'}
            valueStyle={{ color: '#52c41a' }}
          />
          <div
            style={{
              marginTop: 8,
              fontSize: '12px',
              color: '#999',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            学生总数：{completionData.total_students || data?.total_responses}
          </div>
        </Card>
      </Col>

      <Col span={6}>
        <Card>
          <Statistic
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <PercentageOutlined
                  style={{ color: '#1890ff', fontSize: 24 }}
                />
                完成率
              </div>
            }
            value={(completionData.completion_rate || '--') + '%'}
            precision={1}
            valueStyle={{ color: '#1890ff' }}
          />
          <div
            style={{
              marginTop: 8,
              fontSize: '12px',
              color: '#999',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            <Space>
              <span>已完成/学生总数</span>
              {onShowIncompleteStudents && data.incomplete_students_summary && (
                <Button
                  type="link"
                  size="small"
                  style={{ padding: 0, fontSize: '12px' }}
                  onClick={onShowIncompleteStudents}
                >
                  查看未填写(
                  {data.incomplete_students_summary?.total_incomplete || 0})
                </Button>
              )}
            </Space>
          </div>
        </Card>
      </Col>

      <Col span={6}>
        <Card>
          <Statistic
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <UserOutlined style={{ color: '#722ed1', fontSize: 24 }} />
                被评教师数
              </div>
            }
            value={(data.total_teachers_evaluated || '--') + '人'}
            valueStyle={{ color: '#722ed1' }}
          />
          <div
            style={{
              marginTop: 8,
              fontSize: '12px',
              color: '#999',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            平均分：{data.teacher_average_score?.toFixed(1) || 0}分
          </div>
        </Card>
      </Col>
    </Row>
    </>
  );
};

export default SchoolOverview;
