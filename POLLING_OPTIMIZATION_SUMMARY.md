# 📊 统计轮询功能优化总结

## ✅ 优化完成状态

已成功为统计模块添加了智能轮询功能，当触发统计接口返回计算中状态时，页面会自动轮询状态接口直到计算完成。

## 🔄 新增功能特性

### 1. 智能轮询机制
- **自动触发**：当触发统计计算返回 `calculating` 或 `pending` 状态时，自动开始轮询
- **状态监控**：每2秒轮询一次统计状态，实时更新状态显示
- **自动停止**：当状态变为 `completed` 或 `failed` 时，自动停止轮询
- **错误处理**：网络错误或异常状态时自动停止轮询

### 2. 用户界面优化
- **轮询状态显示**：在状态文本中显示 "(自动刷新中)" 标识
- **停止轮询按钮**：轮询期间显示停止按钮，用户可手动停止
- **状态实时更新**：轮询期间状态文本和颜色实时更新

### 3. 资源管理
- **内存清理**：组件卸载时自动清理定时器
- **重复轮询防护**：开始新轮询前自动停止之前的轮询
- **性能优化**：合理的轮询间隔，避免过度请求

## 🛠️ 技术实现详情

### 1. 数据模型层 (`src/models/statistics.ts`)

#### 新增状态管理
```typescript
// 轮询相关状态
const [pollingTimer, setPollingTimer] = useState<NodeJS.Timeout | null>(null);
const [isPolling, setIsPolling] = useState(false);
```

#### 核心轮询函数
```typescript
// 开始轮询统计状态
const startPolling = useCallback(
  (questionnaireId: number, interval: number = 2000) => {
    // 先停止之前的轮询
    stopPolling();
    
    setIsPolling(true);
    
    const poll = async () => {
      try {
        const status = await fetchStatisticsTaskStatus(questionnaireId);
        
        if (status && (status.status === 'completed' || status.status === 'failed')) {
          // 计算完成或失败，停止轮询
          stopPolling();
          
          if (status.status === 'completed') {
            message.success('统计计算完成');
          } else if (status.status === 'failed') {
            message.error(`统计计算失败: ${status.error_message || '未知错误'}`);
          }
        } else if (status && (status.status === 'pending' || status.status === 'calculating')) {
          // 继续轮询
          const timer = setTimeout(poll, interval);
          setPollingTimer(timer);
        } else {
          // 状态异常，停止轮询
          stopPolling();
          message.error('获取统计状态异常');
        }
      } catch (error) {
        // 出错时停止轮询
        stopPolling();
        message.error('轮询统计状态失败');
      }
    };
    
    // 立即执行一次
    poll();
  },
  [fetchStatisticsTaskStatus, stopPolling],
);
```

#### 智能触发统计
```typescript
// 触发统计计算
const triggerStatistics = useCallback(
  async (questionnaireId: number) => {
    setTaskLoading(true);
    try {
      const response = await triggerStatisticsTask({ questionnaire_id: questionnaireId });

      if (response.errCode === 0) {
        message.success('统计任务已启动');
        
        // 如果返回的状态是计算中，开始轮询
        if (response.data && (response.data.status === 'calculating' || response.data.status === 'pending')) {
          startPolling(questionnaireId);
        }
        
        return response.data;
      } else {
        message.error(response.msg || '启动统计任务失败');
        return null;
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
      return null;
    } finally {
      setTaskLoading(false);
    }
  },
  [startPolling],
);
```

### 2. 组件层优化 (`src/pages/Statistics/components/FilterForm.tsx`)

#### 状态显示增强
```typescript
// 获取状态显示文本
const getStatusText = () => {
  if (!statisticsTaskStatus) return '';

  let statusText = '';
  switch (statisticsTaskStatus.status) {
    case 'pending':
      statusText = '等待计算';
      break;
    case 'calculating':
      statusText = '计算中...';
      break;
    case 'completed':
      statusText = `计算完成 (${statisticsTaskStatus.last_calculated_at ? dayjs(statisticsTaskStatus.last_calculated_at).format('MM-DD HH:mm') : ''})`;
      break;
    case 'failed':
      statusText = '计算失败';
      break;
    default:
      statusText = '';
  }
  
  // 如果正在轮询，添加轮询标识
  if (isPolling && (statisticsTaskStatus.status === 'calculating' || statisticsTaskStatus.status === 'pending')) {
    statusText += ' (自动刷新中)';
  }
  
  return statusText;
};
```

#### 停止轮询按钮
```typescript
{isPolling && (
  <Tooltip title="停止自动刷新统计状态">
    <Button
      type="default"
      icon={<StopOutlined />}
      onClick={handleStopPolling}
      size="small"
    >
      停止轮询
    </Button>
  </Tooltip>
)}
```

## 🎯 用户体验提升

### 1. 操作流程优化
1. **触发统计** → 用户点击"触发统计"按钮
2. **自动轮询** → 系统检测到计算中状态，自动开始轮询
3. **状态更新** → 每2秒更新一次状态显示，用户可看到实时进度
4. **完成通知** → 计算完成时显示成功消息，自动停止轮询
5. **手动控制** → 用户可随时点击"停止轮询"按钮中断轮询

### 2. 视觉反馈
- **轮询标识**：状态文本显示 "(自动刷新中)" 标识
- **动态按钮**：轮询期间显示停止按钮
- **颜色状态**：不同状态使用不同颜色标识
- **时间显示**：完成状态显示具体完成时间

### 3. 错误处理
- **网络异常**：自动停止轮询并提示错误
- **状态异常**：检测到异常状态时停止轮询
- **计算失败**：显示具体失败原因

## 📋 配置参数

### 轮询间隔
- **默认间隔**：2000ms (2秒)
- **可配置**：通过 `startPolling(questionnaireId, interval)` 参数调整

### 轮询条件
- **触发条件**：统计状态为 `calculating` 或 `pending`
- **停止条件**：状态变为 `completed` 或 `failed`
- **异常停止**：网络错误或状态异常

## 🔧 使用方法

### 自动轮询
```typescript
// 触发统计后自动开始轮询（无需手动调用）
await triggerStatistics(questionnaireId);
```

### 手动控制轮询
```typescript
// 手动开始轮询
startPolling(questionnaireId, 3000); // 3秒间隔

// 手动停止轮询
stopPolling();
```

### 状态检查
```typescript
// 检查是否正在轮询
if (isPolling) {
  console.log('正在轮询中...');
}
```

## ✅ 测试建议

### 功能测试
1. **触发统计测试**
   - 点击触发统计按钮
   - 验证轮询自动开始
   - 检查状态实时更新

2. **轮询停止测试**
   - 测试计算完成时自动停止
   - 测试手动停止轮询功能
   - 验证错误情况下的停止机制

3. **界面交互测试**
   - 验证轮询状态显示
   - 测试停止按钮的显示/隐藏
   - 检查状态颜色变化

### 性能测试
- 长时间轮询的内存使用情况
- 多次触发统计的资源清理
- 页面切换时的定时器清理

## 🎉 优化完成

✅ **统计轮询功能优化已全部完成！**

现在用户触发统计计算后，系统会智能地自动轮询状态更新，提供更好的用户体验和实时反馈。用户无需手动刷新页面即可看到统计计算的实时进度。
