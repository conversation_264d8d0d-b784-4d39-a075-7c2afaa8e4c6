.statistics-page {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;

  // 卡片样式优化
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 6%);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 10%);
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-weight: 600;
        color: #262626;
      }
    }

    .ant-card-body {
      padding: 20px;
    }
  }

  // 统计卡片样式
  .ant-statistic {
    .ant-statistic-title {
      font-size: 14px;
      color: #8c8c8c;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      font-size: 24px;
      font-weight: 600;

      .ant-statistic-content-value {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  // 表格样式优化
  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
    }

    .ant-table-tbody > tr {
      transition: all 0.2s ease;

      &:hover {
        background: #f5f5f5;
      }

      > td {
        border-bottom: 1px solid #f0f0f0;
      }
    }
  }

  // 搜索表单样式
  .ant-form-inline {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-form-item-label {
      font-weight: 500;
    }
  }

  // 标签样式
  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
  }

  // 按钮样式
  .ant-btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;

    &.ant-btn-primary {
      box-shadow: 0 2px 4px rgba(24, 144, 255, 20%);

      &:hover {
        box-shadow: 0 4px 8px rgba(24, 144, 255, 30%);
        transform: translateY(-1px);
      }
    }

    &.ant-btn-link {
      padding: 0;
      height: auto;
      line-height: 1.5;
    }
  }

  // 模态框样式
  .ant-modal {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;

      .ant-modal-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }

    .ant-modal-body {
      padding: 24px;
    }
  }

  // 空状态样式
  .ant-empty {
    .ant-empty-description {
      color: #8c8c8c;
    }
  }

  // 加载状态样式
  .ant-spin-container {
    min-height: 200px;
  }

  // 筛选表单样式
  .filter-form-card {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(
        45deg,
        transparent,
        rgba(255, 255, 255, 10%),
        transparent
      );
      transform: rotate(45deg);
      transition: all 0.6s ease;
      opacity: 0;
    }

    &:hover::before {
      animation: shimmer 1.5s ease-in-out;
      opacity: 1;
    }

    .ant-form-item-label > label {
      color: white !important;
      font-weight: 500;
    }

    .ant-select {
      .ant-select-selector {
        background: rgba(255, 255, 255, 10%) !important;
        border-color: rgba(255, 255, 255, 30%) !important;
        color: white;

        .ant-select-selection-placeholder {
          color: rgba(255, 255, 255, 70%);
        }
      }

      &:hover .ant-select-selector {
        border-color: rgba(255, 255, 255, 50%) !important;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: rgba(255, 255, 255, 80%) !important;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 20%) !important;
      }
    }
  }

  // 年级统计卡片样式
  .grade-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #1890ff, #722ed1);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 12%);

      &::before {
        opacity: 1;
      }
    }

    .ant-card-body {
      padding: 16px;
    }
  }

  // 统计总数卡片动画
  .total-stats-card {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(
        45deg,
        transparent,
        rgba(255, 255, 255, 10%),
        transparent
      );
      transform: rotate(45deg);
      transition: all 0.6s ease;
      opacity: 0;
    }

    &:hover::before {
      animation: shimmer 1.5s ease-in-out;
      opacity: 1;
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }

    100% {
      transform: translateX(100%) translateY(100%) rotate(45deg);
    }
  }

  // 进度条样式优化
  .ant-progress {
    .ant-progress-bg {
      transition: all 0.3s ease;
    }

    &:hover .ant-progress-bg {
      box-shadow: 0 0 8px rgba(24, 144, 255, 40%);
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .ant-col {
      margin-bottom: 16px;
    }

    .grade-card {
      .ant-card-body {
        padding: 12px;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .ant-card {
      margin-bottom: 16px;
    }

    .ant-form-inline {
      .ant-form-item {
        width: 100%;
        margin-bottom: 16px;
      }
    }

    .ant-table {
      .ant-table-content {
        overflow-x: auto;
      }
    }

    // 移动端年级卡片调整
    .grade-card {
      .ant-card-body {
        padding: 12px 8px;
      }
    }
  }

  @media (max-width: 576px) {
    // 小屏幕下年级卡片改为单列显示
    .ant-row .ant-col {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
}
