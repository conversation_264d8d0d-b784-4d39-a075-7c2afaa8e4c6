import type {
  ICachedIncompleteStudentsResponse,
  IIncompleteStudentInfo,
  IIncompleteStudentsByClass,
  IIncompleteStudentsByGrade,
  IIncompleteStudentsQuery,
  IIncompleteStudentsResponse,
} from '@/types/statistics';
import {
  ExclamationCircleOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import {
  Button,
  Card,
  Col,
  Collapse,
  Empty,
  Form,
  Input,
  Progress,
  Row,
  Select,
  Space,
  Statistic,
  Tag,
  Tooltip,
} from 'antd';
import React, { useState } from 'react';

interface IncompleteStudentsProps {
  data: IIncompleteStudentsResponse | null;
  cachedData?: ICachedIncompleteStudentsResponse | null;
  loading?: boolean;
  useCachedData?: boolean;
  onRefresh?: () => void;
  onPageChange?: (page: number, pageSize: number) => void;
  onFilterChange?: (filters: Partial<IIncompleteStudentsQuery>) => void;
}

/**
 * 未填写学生统计组件
 */
const IncompleteStudents: React.FC<IncompleteStudentsProps> = ({
  data,
  cachedData,
  loading = false,
  useCachedData = false,
  onRefresh,
  onPageChange,
  onFilterChange,
}) => {
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  // 如果使用缓存数据，需要转换数据格式
  const displayData =
    useCachedData && cachedData
      ? {
          summary: {
            total_incomplete: cachedData.total,
            by_grade: [], // 缓存数据中没有年级统计，需要从学生列表中计算
            by_class: [], // 缓存数据中没有班级统计，需要从学生列表中计算
          },
          pagination: {
            page: cachedData.page,
            pageSize: cachedData.pageSize,
            total: cachedData.total,
            totalPages: cachedData.totalPages,
          },
          classes: [], // 缓存数据结构不同，需要转换
        }
      : data;

  if (!displayData || (!displayData.summary && !cachedData)) {
    return (
      <Card title="未填写学生统计" loading={loading}>
        <Empty description="暂无未填写学生数据" />
      </Card>
    );
  }

  // 确保数据不为null
  const safeData = displayData || {
    summary: { total_incomplete: 0, by_grade: [], by_class: [] },
    pagination: { page: 1, pageSize: 20, total: 0, totalPages: 0 },
    classes: [],
  };

  // 学生详情表格列定义
  const studentColumns: ProColumns<IIncompleteStudentInfo>[] = [
    {
      title: '学生姓名',
      dataIndex: 'sso_student_name',
      key: 'sso_student_name',
      width: 120,
      render: (_, record) => (
        <Space>
          <UserOutlined style={{ color: '#1890ff' }} />
          {record.sso_student_name}
        </Space>
      ),
    },
    {
      title: '学生代码',
      dataIndex: 'sso_student_code',
      key: 'sso_student_code',
      width: 120,
    },
    {
      title: '年级',
      dataIndex: 'grade_name',
      key: 'grade_name',
      width: 100,
      render: (_, record) => <Tag color="blue">{record.grade_name}</Tag>,
    },
    {
      title: '班级',
      dataIndex: 'class_name',
      key: 'class_name',
      width: 100,
      render: (_, record) => <Tag color="green">{record.class_name}</Tag>,
    },
  ];

  // 渲染年级统计卡片
  const renderGradeCard = (grade: IIncompleteStudentsByGrade) => {
    const completionRate = grade.completion_rate || 0;
    const isLowCompletion = completionRate < 50;
    const isMediumCompletion = completionRate >= 50 && completionRate < 80;

    return (
      <Col span={6} key={grade.grade_code}>
        <Card
          size="small"
          className="grade-card"
          style={{
            background: isLowCompletion
              ? '#fff2f0'
              : isMediumCompletion
              ? '#fffbe6'
              : '#f6ffed',
            borderColor: isLowCompletion
              ? '#ffccc7'
              : isMediumCompletion
              ? '#ffe58f'
              : '#b7eb8f',
            borderWidth: 1,
            borderStyle: 'solid',
          }}
        >
          <div style={{ textAlign: 'center' }}>
            <div
              style={{
                fontSize: '16px',
                fontWeight: 600,
                color: '#262626',
                marginBottom: 8,
              }}
            >
              {grade.grade_name || '未知年级'}
            </div>

            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 12,
              }}
            >
              <TeamOutlined
                style={{
                  color: isLowCompletion
                    ? '#ff4d4f'
                    : isMediumCompletion
                    ? '#faad14'
                    : '#52c41a',
                  fontSize: '20px',
                  marginRight: 8,
                }}
              />
              <span
                style={{
                  fontSize: '24px',
                  fontWeight: 700,
                  color: isLowCompletion
                    ? '#ff4d4f'
                    : isMediumCompletion
                    ? '#faad14'
                    : '#52c41a',
                }}
              >
                {grade.incomplete_count || 0}
              </span>
              <span
                style={{
                  fontSize: '16px',
                  color: '#8c8c8c',
                  marginLeft: 4,
                }}
              >
                / {grade.total_students || 0}
              </span>
            </div>

            <Progress
              percent={completionRate}
              size="small"
              strokeColor={
                isLowCompletion
                  ? '#ff4d4f'
                  : isMediumCompletion
                  ? '#faad14'
                  : '#52c41a'
              }
              trailColor="#f0f0f0"
              showInfo={false}
              style={{ marginBottom: 8 }}
            />

            <div
              style={{
                fontSize: '13px',
                color: isLowCompletion
                  ? '#ff4d4f'
                  : isMediumCompletion
                  ? '#faad14'
                  : '#52c41a',
                fontWeight: 500,
              }}
            >
              完成率：{completionRate.toFixed(1)}%
            </div>

            {isLowCompletion && (
              <div
                style={{
                  fontSize: '12px',
                  color: '#ff4d4f',
                  marginTop: 4,
                  fontWeight: 500,
                }}
              >
                ⚠️ 完成率偏低
              </div>
            )}
          </div>
        </Card>
      </Col>
    );
  };

  // 渲染班级详情
  const renderClassDetail = (classData: IIncompleteStudentsByClass) => (
    <div key={`${classData.grade_code}-${classData.class_code}`}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <Space>
          <Tag color="blue">{classData.grade_name || '未知年级'}</Tag>
          <Tag color="green">{classData.class_name || '未知班级'}</Tag>
          <span>
            未填写：{classData.incomplete_count || 0} /{' '}
            {classData.total_students || 0}
          </span>
        </Space>
        <Progress
          percent={classData.completion_rate || 0}
          size="small"
          style={{ width: 120 }}
          status={
            (classData.completion_rate || 0) < 80 ? 'exception' : 'success'
          }
        />
      </div>

      {classData.students && classData.students.length > 0 ? (
        <ProTable<IIncompleteStudentInfo>
          columns={studentColumns}
          dataSource={classData.students}
          rowKey="sso_student_code"
          search={false}
          options={false}
          pagination={false}
          size="small"
          scroll={{ x: 500 }}
        />
      ) : (
        <Empty
          description="该班级所有学生都已完成填写"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}
    </div>
  );

  // 处理筛选
  const handleFilter = (values: any) => {
    if (onFilterChange) {
      onFilterChange({
        grade_code: values.grade_code,
        class_code: values.class_code,
      });
    }
  };

  return (
    <Card
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: '#fa8c16' }} />
          未填写学生统计
        </Space>
      }
      extra={
        onRefresh && (
          <Button type="link" onClick={onRefresh} loading={loading}>
            刷新
          </Button>
        )
      }
      loading={loading}
    >
      {/* 筛选表单 */}
      {onFilterChange && (
        <div
          style={{
            marginBottom: 16,
            padding: 16,
            backgroundColor: '#fafafa',
            borderRadius: 6,
          }}
        >
          <Form layout="inline" onFinish={handleFilter}>
            <Form.Item name="grade_code" label="年级">
              <Select placeholder="选择年级" allowClear style={{ width: 120 }}>
                {data?.summary.by_grade?.map((grade) => (
                  <Select.Option
                    key={grade.grade_code}
                    value={grade.grade_code}
                  >
                    {grade.grade_name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="class_code" label="班级">
              <Input
                placeholder="输入班级编码"
                allowClear
                style={{ width: 120 }}
              />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" size="small">
                筛选
              </Button>
            </Form.Item>
          </Form>
        </div>
      )}
      {/* 总体统计 */}
      <div style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title="未填写学生总数"
                value={safeData.summary.total_incomplete || 0}
                suffix="人"
                prefix={
                  <ExclamationCircleOutlined style={{ color: '#f5222d' }} />
                }
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
          <Col span={18}>
            <div style={{ padding: '16px 0' }}>
              <h4 style={{ marginBottom: 16 }}>各年级完成情况</h4>
              <Row gutter={[16, 16]}>
                {safeData.summary.by_grade?.map(renderGradeCard) || []}
              </Row>
            </div>
          </Col>
        </Row>
      </div>

      {/* 班级详情 */}
      {safeData.classes && safeData.classes.length > 0 && (
        <div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 16,
            }}
          >
            <h4 style={{ margin: 0 }}>班级详情</h4>
            <div style={{ fontSize: '12px', color: '#999' }}>
              第 {safeData.pagination.page} 页，共{' '}
              {safeData.pagination.totalPages} 页， 共{' '}
              {safeData.pagination.total} 个班级
            </div>
          </div>
          <Collapse
            activeKey={expandedKeys}
            onChange={(keys) => setExpandedKeys(keys as string[])}
            items={safeData.classes.map((classData) => ({
              key: `${classData.grade_code}-${classData.class_code}`,
              label: (
                <Space>
                  <Tag color="blue">{classData.grade_name || '未知年级'}</Tag>
                  <Tag color="green">{classData.class_name || '未知班级'}</Tag>
                  <span>
                    未填写：{classData.incomplete_count || 0} /{' '}
                    {classData.total_students || 0}
                  </span>
                  <Tooltip
                    title={`完成率：${(classData.completion_rate || 0).toFixed(
                      1,
                    )}%`}
                  >
                    <Progress
                      percent={classData.completion_rate || 0}
                      size="small"
                      style={{ width: 100 }}
                      status={
                        (classData.completion_rate || 0) < 80
                          ? 'exception'
                          : 'success'
                      }
                      showInfo={false}
                    />
                  </Tooltip>
                </Space>
              ),
              children: renderClassDetail(classData),
            }))}
          />

          {/* 分页控件 */}
          {onPageChange && safeData.pagination.totalPages > 1 && (
            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Button
                disabled={safeData.pagination.page <= 1}
                onClick={() =>
                  onPageChange(
                    safeData.pagination.page - 1,
                    safeData.pagination.pageSize,
                  )
                }
                style={{ marginRight: 8 }}
              >
                上一页
              </Button>
              <span style={{ margin: '0 16px' }}>
                {safeData.pagination.page} / {safeData.pagination.totalPages}
              </span>
              <Button
                disabled={
                  safeData.pagination.page >= safeData.pagination.totalPages
                }
                onClick={() =>
                  onPageChange(
                    safeData.pagination.page + 1,
                    safeData.pagination.pageSize,
                  )
                }
              >
                下一页
              </Button>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default IncompleteStudents;
