// @ts-ignore
/* eslint-disable */
import type {
  ICachedIncompleteStudentsQuery,
  ICachedIncompleteStudentsResponse,
  ICachedStatisticsResponse,
  IIncompleteStudentsQuery,
  IIncompleteStudentsResponse,
  IKeywordData,
  ISchoolStatistics,
  IScoreDistribution,
  IStatisticsQuery,
  IStatisticsTaskStatusResponse,
  ITeacherRankingResponse,
  ITeacherStatistics,
  ITriggerStatisticsParams,
  ITriggerStatisticsResponse,
} from '@/types/statistics';
import { request } from '@umijs/max';

/**
 * 统计服务
 * @description 学校统计、教师统计等操作
 */

/** 学校统计概览 GET /api/statistics/school */
export async function getSchoolStatistics(params?: IStatisticsQuery) {
  return request<API.ResType<ISchoolStatistics>>('/api/statistics/school', {
    method: 'GET',
    params,
  });
}

/** 教师排名 GET /api/statistics/teacher-ranking */
export async function getTeacherRanking(params?: IStatisticsQuery) {
  return request<API.ResType<ITeacherRankingResponse>>(
    '/api/statistics/teacher-ranking',
    {
      method: 'GET',
      params,
    },
  );
}

/** 教师统计概览 GET /api/statistics/teacher */
export async function getTeacherStatistics(params?: IStatisticsQuery) {
  return request<API.ResType<ITeacherStatistics>>('/api/statistics/teacher', {
    method: 'GET',
    params,
  });
}

/** 教师评分分布 GET /api/statistics/teacher/:teacherId/distribution */
export async function getTeacherScoreDistribution(
  teacherId: string,
  params?: IStatisticsQuery,
) {
  return request<API.ResType<IScoreDistribution[]>>(
    `/api/statistics/teacher/${teacherId}/distribution`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 教师关键词云 GET /api/statistics/teacher/:teacherId/keywords */
export async function getTeacherKeywords(
  teacherId: string,
  params?: IStatisticsQuery,
) {
  return request<API.ResType<IKeywordData[]>>(
    `/api/statistics/teacher/${teacherId}/keywords`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取未填写学生统计 GET /api/statistics/incomplete-students */
export async function getIncompleteStudents(params: IIncompleteStudentsQuery) {
  return request<API.ResType<IIncompleteStudentsResponse>>(
    '/api/statistics/incomplete-students',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取趋势分析数据 GET /api/statistics/trend */
export async function getTrendAnalysis(params?: IStatisticsQuery) {
  return request<API.ResType<any>>('/api/statistics/trend', {
    method: 'GET',
    params,
  });
}

// ==================== 统计任务相关接口 ====================

/** 触发统计计算 POST /api/statistics/trigger */
export async function triggerStatisticsTask(params: ITriggerStatisticsParams) {
  return request<API.ResType<ITriggerStatisticsResponse>>(
    '/api/statistics/trigger',
    {
      method: 'POST',
      data: params,
    },
  );
}

/** 获取统计任务状态 GET /api/statistics/status/:questionnaireId */
export async function getStatisticsTaskStatus(questionnaireId: number) {
  return request<API.ResType<IStatisticsTaskStatusResponse>>(
    `/api/statistics/status/${questionnaireId}`,
    {
      method: 'GET',
    },
  );
}

/** 获取缓存的统计数据 GET /api/statistics/cached/:questionnaireId */
export async function getCachedStatistics(questionnaireId: number) {
  return request<API.ResType<ICachedStatisticsResponse>>(
    `/api/statistics/cached/${questionnaireId}`,
    {
      method: 'GET',
    },
  );
}

/** 获取缓存的未填写学生列表 GET /api/statistics/cached-incomplete-students */
export async function getCachedIncompleteStudents(
  params: ICachedIncompleteStudentsQuery,
) {
  return request<API.ResType<ICachedIncompleteStudentsResponse>>(
    '/api/statistics/cached-incomplete-students',
    {
      method: 'GET',
      params,
    },
  );
}
